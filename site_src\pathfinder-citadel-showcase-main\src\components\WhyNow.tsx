import { TrendingUp, Users, Clock } from "lucide-react";

const WhyNow = () => {
  const stats = [
    {
      icon: Users,
      number: "2.5M",
      label: "Jobs needed annually",
      description: "Pakistan's growing economy requires massive job creation"
    },
    {
      icon: TrendingUp,
      number: "31%",
      label: "Graduate unemployment",
      description: "Skills gap between education and industry needs"
    },
    {
      icon: Clock,
      number: "2040",
      label: "Demographic window",
      description: "Critical opportunity period for economic transformation"
    }
  ];

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-6 text-primary">Why Now: The Crisis → The Opportunity</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Pakistan stands at a critical juncture. The demographic dividend and digital transformation 
            create an unprecedented opportunity for economic growth through innovation.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div key={index} className="text-center group">
                <div className="w-20 h-20 mx-auto mb-6 bg-gradient-accent rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <Icon className="w-10 h-10 text-accent-foreground" />
                </div>
                <div className="text-5xl font-bold text-primary mb-2">{stat.number}</div>
                <div className="text-xl font-semibold text-accent mb-3">{stat.label}</div>
                <p className="text-muted-foreground">{stat.description}</p>
              </div>
            );
          })}
        </div>

        <div className="text-center bg-gradient-to-r from-primary/5 to-accent/5 rounded-lg p-8">
          <h3 className="text-2xl font-bold text-primary mb-4">Our Outcome Focus</h3>
          <div className="flex flex-col md:flex-row justify-center items-center gap-4 text-lg">
            <span className="bg-gradient-accent bg-clip-text text-transparent font-semibold">Employability</span>
            <span className="text-accent">→</span>
            <span className="bg-gradient-accent bg-clip-text text-transparent font-semibold">Jobs Creation</span>
            <span className="text-accent">→</span>
            <span className="bg-gradient-accent bg-clip-text text-transparent font-semibold">Economic Growth</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhyNow;