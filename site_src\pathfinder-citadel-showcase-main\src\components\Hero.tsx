import { Button } from "@/components/ui/button";
import heroImage from "@/assets/hero-image.jpg";

const Hero = () => {
  return (
    <section className="relative min-h-screen bg-gradient-hero flex items-center overflow-hidden">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0">
        <img 
          src={heroImage} 
          alt="Startup pitch presentation at prestigious conference"
          className="w-full h-full object-cover opacity-20"
        />
        <div className="absolute inset-0 bg-gradient-hero opacity-90" />
      </div>
      
      <div className="container relative z-10 mx-auto px-4 py-20">
        <div className="max-w-4xl mx-auto text-center text-primary-foreground">
          {/* Event Details Bar */}
          <div className="inline-flex items-center gap-2 bg-primary-foreground/10 backdrop-blur-sm border border-primary-foreground/20 rounded-full px-6 py-3 mb-8">
            <div className="w-2 h-2 bg-accent rounded-full animate-pulse" />
            <span className="text-sm font-medium">Pakistan Pavilion, Davos — Jan 19–23, 2026</span>
          </div>
          
          {/* Main Headline */}
          <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
            Pathfinder CITADEL
            <span className="block bg-gradient-accent bg-clip-text text-transparent">
              Launching Pakistan's
            </span>
            Innovation Revolution
          </h1>
          
          {/* Tagline */}
          <p className="text-2xl md:text-3xl mb-8 opacity-90 font-medium">
            Create | Innovate | Lead
          </p>
          
          {/* Subheadline */}
          <p className="text-xl md:text-2xl mb-10 opacity-90 max-w-3xl mx-auto leading-relaxed">
            Pakistan's Center for Innovation, Technological Advancement, Digital Entrepreneurs and Leadership
          </p>
          
          {/* CTAs */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Button 
              size="lg" 
              variant="hero"
              className="text-lg px-8 py-4 min-w-[200px] shadow-accent hover:shadow-premium transition-all duration-300"
              onClick={() => document.getElementById('programs')?.scrollIntoView({ behavior: 'smooth' })}
            >
              Explore Programs
            </Button>
            <Button 
              size="lg" 
              variant="outline-hero"
              className="text-lg px-8 py-4 min-w-[200px]"
              onClick={() => document.getElementById('startup-challenge')?.scrollIntoView({ behavior: 'smooth' })}
            >
              Davos Startup Challenge
            </Button>
          </div>
          
          {/* Trust Line */}
          <p className="text-sm opacity-75">
            Organized by Pathfinder CITADEL
          </p>
        </div>
      </div>
      
      {/* Decorative Elements */}
      <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-background to-transparent" />
    </section>
  );
};

export default Hero;