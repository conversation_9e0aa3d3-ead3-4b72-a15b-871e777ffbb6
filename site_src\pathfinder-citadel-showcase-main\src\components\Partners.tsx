import { Button } from "@/components/ui/button";
import { Mail } from "lucide-react";

const Partners = () => {
  const partners = [
    { name: "Pathfinder Group", description: "Leading Financial Services Company" },
  ];

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4 text-primary">Partners & Supporters</h2>
          <p className="text-xl text-muted-foreground">
            Backed by leading organizations committed to Pakistan's startup ecosystem
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-1 gap-8 max-w-2xl mx-auto mb-12">
          {partners.map((partner, index) => (
            <div 
              key={index} 
              className="bg-card rounded-lg p-6 shadow-card hover:shadow-premium transition-all duration-300 text-center group"
            >
              <div className="w-20 h-20 mx-auto mb-4 bg-gradient-accent rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <span className="text-accent-foreground font-bold text-sm">
                  {partner.name.slice(0, 2)}
                </span>
              </div>
              <h3 className="font-bold text-primary mb-2">{partner.name}</h3>
              <p className="text-xs text-muted-foreground">{partner.description}</p>
            </div>
          ))}
        </div>

        <div className="text-center">
          <div className="bg-card rounded-lg p-8 max-w-md mx-auto shadow-card">
            <h3 className="text-xl font-bold text-primary mb-4">Become a Partner</h3>
            <p className="text-muted-foreground mb-6">
              Join us in supporting Pakistan's most promising startups
            </p>
            <Button variant="outline" size="lg" asChild>
              <a href="mailto:<EMAIL>?subject=Partnership Inquiry - Pathfinder CITADEL">
                <Mail className="w-4 h-4 mr-2" />
                Partner With Us
              </a>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Partners;