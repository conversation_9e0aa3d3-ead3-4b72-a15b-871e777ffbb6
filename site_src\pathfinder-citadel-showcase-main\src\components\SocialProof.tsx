import { Card, CardContent } from "@/components/ui/card";
import { Users, MapPin, CreditCard, Shield } from "lucide-react";

const SocialProof = () => {
  const pathfinderStats = [
    {
      icon: Users,
      number: "15,000+",
      label: "Employees Nationwide",
      description: "Largest integrated security & financial services group"
    },
    {
      icon: MapPin,
      number: "132+",
      label: "Cities & Towns",
      description: "Comprehensive geographic coverage across Pakistan"
    },
    {
      icon: CreditCard,
      number: "13.1M",
      label: "Asaan Mobile Accounts",
      description: "Leading financial inclusion initiative - Banking the Unbanked"
    },
    {
      icon: Shield,
      number: "25+",
      label: "Years of Excellence",
      description: "Trusted leader in security and financial services"
    }
  ];

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-6 text-primary">Powered by Pathfinder Group</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            CITADEL is backed by Pakistan's largest integrated security and financial services group, 
            bringing decades of execution excellence and nationwide reach.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {pathfinderStats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index} className="shadow-card hover:shadow-premium transition-all duration-300 group text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gradient-accent rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-8 h-8 text-accent-foreground" />
                  </div>
                  <div className="text-3xl font-bold text-accent mb-2">{stat.number}</div>
                  <h3 className="font-bold text-primary mb-3">{stat.label}</h3>
                  <p className="text-sm text-muted-foreground">{stat.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Key Achievements */}
        <div className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-lg p-8">
          <h3 className="text-2xl font-bold text-primary mb-8 text-center">Key Achievements & Impact</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h4 className="text-xl font-semibold text-accent mb-4">Financial Inclusion Leadership</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-accent rounded-full flex-shrink-0 mt-2"></div>
                  <span>13.1M Asaan Mobile Accounts - largest financial inclusion program</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-accent rounded-full flex-shrink-0 mt-2"></div>
                  <span>Banking the Unbanked initiative reaching rural communities</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-accent rounded-full flex-shrink-0 mt-2"></div>
                  <span>Digital payment infrastructure across Pakistan</span>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-xl font-semibold text-accent mb-4">Security & Technology Excellence</h4>
              <ul className="space-y-2 text-muted-foreground">
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-accent rounded-full flex-shrink-0 mt-2"></div>
                  <span>Advanced security solutions for enterprises and government</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-accent rounded-full flex-shrink-0 mt-2"></div>
                  <span>Technology infrastructure and digital transformation</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-accent rounded-full flex-shrink-0 mt-2"></div>
                  <span>Proven track record in large-scale implementation</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SocialProof;