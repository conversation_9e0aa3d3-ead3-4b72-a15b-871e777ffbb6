import { Card, CardContent } from "@/components/ui/card";
import { Building, MapPin, Users, Briefcase } from "lucide-react";

const EcosystemInfrastructure = () => {
  const infrastructure = [
    {
      icon: Building,
      title: "26+ Software Technology Parks",
      description: "Nationwide network of technology hubs"
    },
    {
      icon: MapPin,
      title: "5 NASTP Parks",
      description: "National Science & Technology Parks across Pakistan"
    },
    {
      icon: Building,
      title: "9 InnoVista IT Centers",
      description: "Innovation and incubation centers"
    },
    {
      icon: Users,
      title: "NSTP at NUST Islamabad",
      description: "Leading university-based science park"
    }
  ];

  const partners = [
    { name: "MoITT", description: "Ministry of IT & Telecom" },
    { name: "PSEB", description: "Punjab Small Industries Corporation" },
    { name: "Ignite", description: "National Technology Fund" },
    { name: "NASTP/NSTP", description: "National Science & Technology Parks" },
    { name: "PITB", description: "Punjab IT Board" },
    { name: "KPITB", description: "Khyber Pakhtunkhwa IT Board" },
    { name: "HEC", description: "Higher Education Commission" },
    { name: "MoP", description: "Ministry of Planning" }
  ];

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-6 text-primary">Ecosystem & Infrastructure</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Leveraging Pakistan's robust innovation infrastructure and national partnerships 
            to create a comprehensive ecosystem for technology development.
          </p>
        </div>

        {/* National Infrastructure */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-primary mb-8 text-center">National Infrastructure</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {infrastructure.map((item, index) => {
              const Icon = item.icon;
              return (
                <Card key={index} className="shadow-card hover:shadow-premium transition-all duration-300 group">
                  <CardContent className="p-6 text-center">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gradient-accent rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Icon className="w-8 h-8 text-accent-foreground" />
                    </div>
                    <h4 className="font-bold text-primary mb-2">{item.title}</h4>
                    <p className="text-sm text-muted-foreground">{item.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* How We Leverage */}
        <div className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-lg p-8 mb-16">
          <h3 className="text-2xl font-bold text-primary mb-6 text-center">How We Leverage This Infrastructure</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="w-12 h-12 mx-auto mb-3 bg-accent rounded-full flex items-center justify-center">
                <Building className="w-6 h-6 text-accent-foreground" />
              </div>
              <h4 className="font-semibold text-primary mb-2">Co-location</h4>
              <p className="text-sm text-muted-foreground">Shared spaces and resources</p>
            </div>
            <div>
              <div className="w-12 h-12 mx-auto mb-3 bg-accent rounded-full flex items-center justify-center">
                <Users className="w-6 h-6 text-accent-foreground" />
              </div>
              <h4 className="font-semibold text-primary mb-2">Testbeds</h4>
              <p className="text-sm text-muted-foreground">Real-world testing environments</p>
            </div>
            <div>
              <div className="w-12 h-12 mx-auto mb-3 bg-accent rounded-full flex items-center justify-center">
                <Briefcase className="w-6 h-6 text-accent-foreground" />
              </div>
              <h4 className="font-semibold text-primary mb-2">Prototyping Labs</h4>
              <p className="text-sm text-muted-foreground">Advanced development facilities</p>
            </div>
            <div>
              <div className="w-12 h-12 mx-auto mb-3 bg-accent rounded-full flex items-center justify-center">
                <MapPin className="w-6 h-6 text-accent-foreground" />
              </div>
              <h4 className="font-semibold text-primary mb-2">Commercialization</h4>
              <p className="text-sm text-muted-foreground">Market-ready product development</p>
            </div>
          </div>
        </div>

        {/* National Collaborators */}
        <div className="text-center">
          <h3 className="text-2xl font-bold text-primary mb-8">National Collaborators</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {partners.map((partner, index) => (
              <div key={index} className="bg-card p-4 rounded-lg shadow-card hover:shadow-premium transition-all duration-300 group">
                <div className="w-12 h-12 mx-auto mb-3 bg-gradient-accent rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <span className="text-accent-foreground font-bold text-xs">
                    {partner.name.slice(0, 2)}
                  </span>
                </div>
                <h4 className="font-bold text-primary text-sm mb-1">{partner.name}</h4>
                <p className="text-xs text-muted-foreground">{partner.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default EcosystemInfrastructure;