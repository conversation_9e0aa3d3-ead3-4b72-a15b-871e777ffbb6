import { <PERSON>, CardContent } from "@/components/ui/card";
import { Linkedin, Mail } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

const FeaturedLeadership = () => {
  const leaders = [
    {
      name: "<PERSON><PERSON><PERSON>",
      title: "Chairman, Pathfinder Group",
      description: "Visionary leader with decades of experience in security services and financial inclusion. Pioneer of Banking the Unbanked initiative.",
      expertise: ["Strategic Leadership", "Financial Inclusion", "Security Services", "National Development"]
    },
    {
      name: "<PERSON><PERSON><PERSON>", 
      title: "Co-Chairman, Pathfinder Group",
      description: "Technology and innovation leader driving digital transformation across Pakistan's financial and security sectors.",
      expertise: ["Digital Transformation", "Technology Strategy", "Innovation Management", "Business Development"]
    }
  ];

  const advisors = [
    {
      name: "Dr. <PERSON>",
      title: "Harvard/Hult • EDIE Co-creator",
      description: "Global innovation expert and co-creator of the EDIE framework for entrepreneur-driven innovation ecosystems."
    },
    {
      name: "Dr. <PERSON>",
      title: "Global Strategy & Innovation • WEF Network",
      description: "World Economic Forum network member specializing in global strategy and innovation policy."
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      title: "Global Catalyst • Multiple Unicorn IPOs",
      description: "Serial entrepreneur and investor with track record of building and scaling unicorn companies globally."
    }
  ];

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-6 text-primary">Featured Leadership</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            World-class leadership combining local expertise with global vision, 
            supported by internationally renowned advisors and experts.
          </p>
        </div>

        {/* Core Leadership */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-primary mb-8 text-center">Core Leadership</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {leaders.map((leader, index) => (
              <Card key={index} className="shadow-card hover:shadow-premium transition-all duration-300">
                <CardContent className="p-8">
                  <div className="text-center mb-6">
                    <div className="w-24 h-24 mx-auto mb-4 bg-gradient-accent rounded-full flex items-center justify-center">
                      <span className="text-2xl font-bold text-accent-foreground">
                        {leader.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <h4 className="text-xl font-bold text-primary mb-2">{leader.name}</h4>
                    <p className="text-accent font-semibold mb-4">{leader.title}</p>
                  </div>
                  
                  <p className="text-muted-foreground mb-6 text-center">{leader.description}</p>
                  
                  <div className="mb-6">
                    <h5 className="font-semibold text-primary mb-3">Key Expertise:</h5>
                    <div className="flex flex-wrap gap-2">
                      {leader.expertise.map((skill, skillIndex) => (
                        <span key={skillIndex} className="bg-accent/10 text-accent px-3 py-1 rounded-full text-sm">
                          {skill}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="flex justify-center gap-2">
                    <Button variant="outline" size="sm">
                      <Linkedin className="w-4 h-4 mr-2" />
                      LinkedIn
                    </Button>
                    <Button variant="outline" size="sm">
                      <Mail className="w-4 h-4 mr-2" />
                      Contact
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* International Experts & Advisory */}
        <div>
          <h3 className="text-2xl font-bold text-primary mb-8 text-center">International Experts & Advisory</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {advisors.map((advisor, index) => (
              <Card key={index} className="shadow-card hover:shadow-premium transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gradient-accent rounded-full flex items-center justify-center">
                    <span className="text-lg font-bold text-accent-foreground">
                      {advisor.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                    </span>
                  </div>
                  <h4 className="font-bold text-primary mb-2">{advisor.name}</h4>
                  <p className="text-accent text-sm font-semibold mb-3">{advisor.title}</p>
                  <p className="text-muted-foreground text-sm">{advisor.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Impact Statement */}
        <div className="mt-16 bg-gradient-to-r from-primary/5 to-accent/5 rounded-lg p-8 text-center">
          <h3 className="text-2xl font-bold text-primary mb-4">From Vision to Execution</h3>
          <p className="text-xl text-muted-foreground mb-6 max-w-3xl mx-auto">
            Our leadership team combines decades of local execution excellence with world-class 
            advisory support to implement the CITADEL Masterplan for Pakistan's innovation transformation.
          </p>
          <Button size="lg" variant="outline">
            Learn About Our Impact
          </Button>
        </div>
      </div>
    </section>
  );
};

export default FeaturedLeadership;