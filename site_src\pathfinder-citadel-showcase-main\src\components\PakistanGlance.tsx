import { Card, CardContent } from "@/components/ui/card";
import { Globe, Users, TrendingUp, Zap } from "lucide-react";

const PakistanGlance = () => {
  const highlights = [
    {
      icon: Users,
      title: "5th Largest Population",
      description: "Young workforce with tremendous potential",
      stat: "230M+"
    },
    {
      icon: TrendingUp,
      title: "6th Largest Labor Force",
      description: "Massive human capital ready for transformation",
      stat: "75M+"
    },
    {
      icon: Globe,
      title: "Strategic Location",
      description: "Gateway between South Asia, Central Asia, and Middle East",
      stat: "3 Regions"
    },
    {
      icon: Zap,
      title: "Digital Potential",
      description: "Software is the new oil - unlimited export potential",
      stat: "Unlimited"
    }
  ];

  const comparatives = [
    "Karnataka, India: IT export success story",
    "China's innovation economy transformation", 
    "Ethiopia & Indonesia: Rapid economic growth patterns",
    "Software exports: Unlimited scalability potential"
  ];

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-6 text-primary">Pakistan at a Glance: The Opportunity</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Pakistan has all the ingredients for becoming a global innovation powerhouse. 
            The opportunity is unprecedented, and the time is now.
          </p>
        </div>

        {/* Key Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          {highlights.map((highlight, index) => {
            const Icon = highlight.icon;
            return (
              <Card key={index} className="shadow-card hover:shadow-premium transition-all duration-300 group text-center">
                <CardContent className="p-6">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gradient-accent rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-8 h-8 text-accent-foreground" />
                  </div>
                  <div className="text-2xl font-bold text-accent mb-2">{highlight.stat}</div>
                  <h3 className="font-bold text-primary mb-2">{highlight.title}</h3>
                  <p className="text-sm text-muted-foreground">{highlight.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Gap to Potential */}
        <div className="bg-gradient-to-r from-primary/5 to-accent/5 rounded-lg p-8 mb-16">
          <h3 className="text-2xl font-bold text-primary mb-6 text-center">Gap-to-Potential Analysis</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div>
              <div className="text-3xl font-bold text-accent mb-2">Competitiveness</div>
              <p className="text-muted-foreground">Significant room for improvement in global rankings</p>
            </div>
            <div>
              <div className="text-3xl font-bold text-accent mb-2">Entrepreneurship</div>
              <p className="text-muted-foreground">Untapped entrepreneurial energy waiting to be unleashed</p>
            </div>
            <div>
              <div className="text-3xl font-bold text-accent mb-2">Innovation</div>
              <p className="text-muted-foreground">Massive potential in technology and innovation indices</p>
            </div>
          </div>
        </div>

        {/* Comparative Examples */}
        <div className="text-center">
          <h3 className="text-2xl font-bold text-primary mb-6">Learning from Global Success Stories</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl mx-auto">
            {comparatives.map((comparative, index) => (
              <div key={index} className="bg-card p-4 rounded-lg shadow-card">
                <p className="text-muted-foreground">{comparative}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default PakistanGlance;