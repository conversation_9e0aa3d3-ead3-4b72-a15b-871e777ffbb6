import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Phone, Mail, Download, ExternalLink } from "lucide-react";

const Contact = () => {
  const projectDetails = {
    cost: "PKR 350,000",
    timeline: "1 month",
    deliverables: [
      "Complete UI/UX Design",
      "Responsive Landing Page",
      "Contact Form Integration", 
      "SEO Optimization",
      "One Month Support"
    ]
  };

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <div>
              <h2 className="text-4xl font-bold mb-6 text-primary">Contact Us</h2>
              <p className="text-xl text-muted-foreground mb-8">
                Get in touch for applications, partnerships, or any questions about the Pathfinder CITADEL DAVOS Startup Challenge.
              </p>

              <div className="space-y-6 mb-8">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-accent rounded-full flex items-center justify-center">
                    <Phone className="w-6 h-6 text-accent-foreground" />
                  </div>
                  <div>
                    <p className="font-semibold text-primary">Phone</p>
                    <a href="tel:+923135644041" className="text-muted-foreground hover:text-accent transition-colors">
                      +92 313 5644041
                    </a>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-accent rounded-full flex items-center justify-center">
                    <Mail className="w-6 h-6 text-accent-foreground" />
                  </div>
                  <div>
                    <p className="font-semibold text-primary">Email</p>
                    <a href="mailto:<EMAIL>" className="text-muted-foreground hover:text-accent transition-colors">
                      <EMAIL>
                    </a>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button variant="accent" size="lg" asChild>
                  <a href="mailto:<EMAIL>?subject=Pathfinder CITADEL - Partnership Inquiry">
                    <Mail className="w-4 h-4 mr-2" />
                    Send Email
                  </a>
                </Button>
                <Button variant="outline" size="lg">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  LinkedIn Profile
                </Button>
              </div>
            </div>

            {/* Project Details */}
            <Card className="shadow-card border-0">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="w-5 h-5 text-accent" />
                  Website Development Project
                </CardTitle>
                <CardDescription>
                  Complete development details for the Pathfinder CITADEL website
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex justify-between items-center p-4 bg-accent/10 rounded-lg">
                  <span className="font-semibold text-primary">Project Cost:</span>
                  <span className="text-xl font-bold text-accent">{projectDetails.cost}</span>
                </div>

                <div className="flex justify-between items-center p-4 bg-muted/50 rounded-lg">
                  <span className="font-semibold text-primary">Timeline:</span>
                  <span className="text-lg font-semibold text-primary">{projectDetails.timeline}</span>
                </div>

                <div>
                  <h4 className="font-semibold text-primary mb-3">Deliverables:</h4>
                  <ul className="space-y-2">
                    {projectDetails.deliverables.map((deliverable, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-accent rounded-full" />
                        <span className="text-muted-foreground text-sm">{deliverable}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="space-y-3">
                  <Button variant="accent" size="lg" className="w-full">
                    <Download className="w-4 h-4 mr-2" />
                    Download Project Brief (PDF)
                  </Button>
                  
                  <Button variant="outline" size="lg" className="w-full" asChild>
                    <a href="mailto:<EMAIL>?subject=Hire Us - Pathfinder CITADEL Website Development">
                      Hire Us for Development
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;