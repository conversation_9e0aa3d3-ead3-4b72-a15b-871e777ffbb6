import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Mail, Upload, CheckCircle } from "lucide-react";

const ApplicationForm = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsSubmitting(true);

    const formData = new FormData(e.currentTarget);
    const data = Object.fromEntries(formData);

    try {
      // Simulate API call or integrate with webhook
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Send email notification (placeholder)
      const emailBody = `
        New Startup Application:
        
        Company: ${data.startupName}
        Description: ${data.description}
        Founder(s): ${data.founders}
        Email: ${data.email}
        Phone: ${data.phone}
        Location: ${data.location}
        Sector: ${data.sector}
        Website: ${data.website}
        
        Application submitted at: ${new Date().toISOString()}
      `;
      
      // Create mailto link as fallback
      const mailtoLink = `mailto:<EMAIL>?subject=New Pathfinder CITADEL Application - ${data.startupName}&body=${encodeURIComponent(emailBody)}`;
      
      setIsSubmitted(true);
      toast({
        title: "Application Submitted Successfully!",
        description: "Thanks — your application has been received. We'll contact shortlisted teams within 2 weeks.",
      });

      // Optional: Open email client
      if (window.confirm("Would you like to send a copy via email?")) {
        window.open(mailtoLink);
      }
    } catch (error) {
      toast({
        title: "Submission Error",
        description: "Please complete required fields and try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="text-center py-16">
        <CheckCircle className="mx-auto mb-6 h-16 w-16 text-accent" />
        <h3 className="text-2xl font-bold mb-4 text-primary">Application Submitted!</h3>
        <p className="text-muted-foreground max-w-md mx-auto">
          Thanks — your application has been received. We'll contact shortlisted teams within 2 weeks.
        </p>
      </div>
    );
  }

  return (
    <section id="application-form" className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold mb-4 text-primary">Apply Now</h2>
            <p className="text-xl text-muted-foreground">
              Submit your application for the Pathfinder CITADEL DAVOS Startup Challenge
            </p>
          </div>

          <Card className="shadow-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5 text-accent" />
                Application Form
              </CardTitle>
              <CardDescription>
                All fields marked with * are required
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="startupName">Startup Name *</Label>
                    <Input 
                      id="startupName" 
                      name="startupName" 
                      required 
                      className="mt-1"
                      placeholder="Your company name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="sector">Sector *</Label>
                    <Select name="sector" required>
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select your sector" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ai-ml">AI & Machine Learning</SelectItem>
                        <SelectItem value="fintech">Fintech</SelectItem>
                        <SelectItem value="climate">Climate / Greentech</SelectItem>
                        <SelectItem value="healthtech">HealthTech</SelectItem>
                        <SelectItem value="deeptech">DeepTech / Industry 4.0</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Short Description (280 chars max) *</Label>
                  <Textarea 
                    id="description" 
                    name="description" 
                    required 
                    maxLength={280}
                    rows={3}
                    className="mt-1 resize-none"
                    placeholder="Brief description of your startup and solution"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="founders">Founder(s) Name(s) *</Label>
                    <Input 
                      id="founders" 
                      name="founders" 
                      required 
                      className="mt-1"
                      placeholder="Founder names"
                    />
                  </div>
                  <div>
                    <Label htmlFor="location">City, Country *</Label>
                    <Input 
                      id="location" 
                      name="location" 
                      required 
                      className="mt-1"
                      placeholder="Karachi, Pakistan"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="email">Contact Email *</Label>
                    <Input 
                      id="email" 
                      name="email" 
                      type="email" 
                      required 
                      className="mt-1"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Contact Phone *</Label>
                    <Input 
                      id="phone" 
                      name="phone" 
                      type="tel" 
                      required 
                      className="mt-1"
                      placeholder="+92 300 1234567"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="website">Website / Product URL</Label>
                  <Input 
                    id="website" 
                    name="website" 
                    type="url" 
                    className="mt-1"
                    placeholder="https://yourstartup.com"
                  />
                </div>

                <div className="space-y-4">
                  <Label className="text-sm font-medium">Attachments (Upload or provide links)</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="pitchDeck" className="text-sm">Pitch Deck (PDF)</Label>
                      <div className="mt-1 flex items-center gap-2">
                        <Upload className="h-4 w-4 text-muted-foreground" />
                        <Input 
                          id="pitchDeck" 
                          name="pitchDeck" 
                          type="file" 
                          accept=".pdf"
                          className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-accent file:text-accent-foreground hover:file:bg-accent/80"
                        />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="demoVideo" className="text-sm">Demo Video Link</Label>
                      <Input 
                        id="demoVideo" 
                        name="demoVideo" 
                        type="url" 
                        className="mt-1"
                        placeholder="YouTube/Vimeo link"
                      />
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox id="consent" name="consent" required />
                  <Label htmlFor="consent" className="text-sm">
                    I agree to the{" "}
                    <a href="#" className="text-accent hover:underline">
                      terms & privacy policy
                    </a>
                    . By applying you consent to storing your info for selection purposes. *
                  </Label>
                </div>

                <Button 
                  type="submit" 
                  size="lg" 
                  variant="accent"
                  disabled={isSubmitting}
                  className="w-full py-4 text-lg"
                >
                  {isSubmitting ? "Submitting Application..." : "Submit Application"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default ApplicationForm;