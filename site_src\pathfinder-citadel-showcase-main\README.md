# Pathfinder CITADEL Startup Challenge — Davos 2026

A complete, modern, responsive landing page for the Pathfinder CITADEL Startup Challenge, showcasing Pakistan's top startups at the World Economic Forum in Davos 2026.

## 🚀 Project Overview

**Project Cost:** PKR 350,000  
**Timeline:** 1 month  
**Contact:** <EMAIL> | +92 313 5644041  

## 📋 Features

- **Modern Design:** Professional navy blue & gold color scheme with premium aesthetics
- **Fully Responsive:** Optimized for all devices and screen sizes
- **SEO Optimized:** Complete meta tags, Open Graph, and JSON-LD structured data
- **Accessibility:** WCAG AA compliant with keyboard navigation and screen reader support
- **Interactive Application Form:** Complete startup application with file uploads and validation
- **Performance Optimized:** Fast loading with optimized images and smooth animations

## 🛠 Technical Stack

- **Frontend:** React 18 + TypeScript
- **Styling:** Tailwind CSS with custom design system
- **UI Components:** Shadcn/ui components with custom variants
- **Build Tool:** Vite
- **Icons:** Lucide React
- **Forms:** React Hook Form with Zod validation
- **Animations:** Custom CSS animations and Tailwind transitions

## 🎨 Design System

### Brand Colors
- **Primary (Navy):** #08203A
- **Accent (Gold):** #C49A2C  
- **Secondary (Teal):** #0EA5A4
- **Background:** #F7FAFB

### Typography
- **Primary Font:** Inter (Google Fonts)
- **Font Weights:** 300, 400, 500, 600, 700

### Components
- Custom button variants (hero, accent, outline-hero)
- Gradient backgrounds and premium shadows
- Smooth hover effects and micro-animations

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd pathfinder-citadel-challenge
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Build for production**
   ```bash
   npm run build
   ```

5. **Preview production build**
   ```bash
   npm run preview
   ```

### Static Export
To create a static export for hosting:
```bash
npm run build
# The build folder contains the static files ready for deployment
```

## 📝 Content Management

### Updating Content
- **Hero Headlines:** Edit `src/components/Hero.tsx`
- **Application Form:** Configure in `src/components/ApplicationForm.tsx`
- **Timeline Dates:** Update in `src/components/Timeline.tsx`
- **Contact Info:** Modify in `src/components/Contact.tsx` and `src/components/Footer.tsx`

### Alternative Headlines (A/B Testing)
The project includes multiple headline variations:

**Headline Variations:**
- A: "Showcase Pakistan's Top Startups at Davos — Jan 19–23, 2026"
- B: "Represent Pakistan at the World Economic Forum — Apply Today"  
- C: "Win a Spot at the Pakistan Pavilion, Davos 2026"

**Subheadline Variations:**
- Short: "Apply for mentorship, media exposure & global investor access."
- Medium: "Apply now for mentoring, media exposure and a chance to present at the Pakistan Pavilion — Davos 19–23 Jan 2026."
- Long: "Apply now for the Pathfinder CITADEL Startup Challenge and win a chance to present at the Pakistan Pavilion, World Economic Forum, Davos."

## 📧 Form Integration

### Current Setup
- Form submissions display success message
- Email integration via mailto links
- Placeholder for webhook integration

### Integration Options

1. **Email Integration (Current)**
   ```javascript
   // Mailto fallback in ApplicationForm.tsx
   const mailtoLink = `mailto:<EMAIL>?subject=...`;
   ```

2. **Webhook Integration**
   ```javascript
   // Replace the API call in handleSubmit function
   const response = await fetch('YOUR_WEBHOOK_URL', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify(formData)
   });
   ```

3. **Google Sheets Integration**
   - Use Google Apps Script webhook
   - Configure in `src/components/ApplicationForm.tsx`

4. **Zapier Integration**
   - Create Zapier webhook trigger
   - Update form submission endpoint

## 🔧 Deployment

### Netlify
1. Connect your GitHub repository
2. Build command: `npm run build`
3. Publish directory: `dist`
4. Deploy

### Vercel
1. Import project from GitHub
2. Framework Preset: Vite
3. Build Command: `npm run build`
4. Output Directory: `dist`
5. Deploy

### GitHub Pages
1. Enable GitHub Pages in repository settings
2. Build and deploy:
   ```bash
   npm run build
   # Upload dist folder to gh-pages branch
   ```

### Manual Hosting
1. Run `npm run build`
2. Upload `dist` folder contents to your web server
3. Configure server for SPA routing (if needed)

## 🔍 SEO Checklist

### ✅ Completed
- Meta title and description
- Open Graph tags for social sharing
- Twitter Card meta tags
- JSON-LD structured data (Event schema)
- Semantic HTML structure
- Image alt attributes
- Responsive design
- Fast loading times

### 🔄 Additional SEO Tasks
- [ ] Add Google Analytics tracking code
- [ ] Create and submit sitemap.xml
- [ ] Add robots.txt (basic version included)
- [ ] Set up Google Search Console
- [ ] Add canonical URLs for production
- [ ] Implement schema markup for Organization

## ♿ Accessibility Features

- **Keyboard Navigation:** Full keyboard support for all interactive elements
- **Screen Reader Support:** Proper ARIA labels and semantic HTML
- **Color Contrast:** WCAG AA compliant color ratios
- **Focus Management:** Clear focus indicators
- **Skip Links:** Skip to main content functionality
- **Alternative Text:** Descriptive alt text for all images

## 📊 Analytics Setup

### Google Analytics 4
Add your GA4 measurement ID to `index.html`:
```html
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

### Event Tracking
Key events to track:
- Application form submissions
- PDF downloads
- Email link clicks
- Phone number clicks
- Social media clicks

## 🔒 Privacy & Compliance

### Included
- Privacy notice in application form
- Cookie banner placeholder
- Terms & conditions links in footer

### Additional Setup
- [ ] Create detailed Privacy Policy page
- [ ] Implement cookie consent management
- [ ] Add Terms & Conditions page
- [ ] Configure GDPR compliance (if applicable)

## 📱 Performance Optimization

### Current Optimizations
- Optimized images (WebP format recommended)
- Minimal JavaScript bundle
- CSS-in-JS eliminated for better performance
- Lazy loading for images
- Efficient animations

### Additional Improvements
- [ ] Implement image lazy loading
- [ ] Add service worker for caching
- [ ] Optimize font loading
- [ ] Compress assets further

## 🛡️ Security

### Best Practices Implemented
- Form validation and sanitization
- No sensitive data in frontend code
- Secure external links (rel="noopener")
- Content Security Policy ready

## 📞 Support & Maintenance

### 1 Month Support Included
- Bug fixes and minor adjustments
- Content updates and modifications
- Performance optimizations
- Email integration assistance

### Contact for Support
**Developer:** Imran Jattala  
**Email:** <EMAIL>  
**Phone:** +92 313 5644041

## 📄 License

This project is proprietary to Pathfinder CITADEL. All rights reserved.

---

**Built with ❤️ for Pathfinder CITADEL Startup Challenge**