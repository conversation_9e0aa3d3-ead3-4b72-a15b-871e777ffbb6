import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Mail, Phone, Linkedin, Twitter, Instagram } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          {/* Brand & Logo */}
          <div className="col-span-1 md:col-span-2">
            <div className="mb-6">
              <h3 className="text-2xl font-bold mb-2">Pathfinder CITADEL</h3>
              <p className="text-lg text-accent italic">Create | Innovate | Lead</p>
            </div>
            <p className="text-primary-foreground/80 mb-6 max-w-md">
              Center for Innovation, Technological Advancement, Digital Entrepreneurs & Leadership. 
              Showcasing Pakistan's top startups at the World Economic Forum, Davos 2026.
            </p>
            <div className="flex gap-4">
              <Button variant="ghost" size="icon" className="text-primary-foreground hover:text-accent hover:bg-primary-foreground/10">
                <Linkedin className="w-5 h-5" />
              </Button>
              <Button variant="ghost" size="icon" className="text-primary-foreground hover:text-accent hover:bg-primary-foreground/10">
                <Twitter className="w-5 h-5" />
              </Button>
              <Button variant="ghost" size="icon" className="text-primary-foreground hover:text-accent hover:bg-primary-foreground/10">
                <Instagram className="w-5 h-5" />
              </Button>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <a href="#about" className="text-primary-foreground/80 hover:text-accent transition-colors">
                  About CITADEL
                </a>
              </li>
              <li>
                <a href="#timeline" className="text-primary-foreground/80 hover:text-accent transition-colors">
                  Application Process
                </a>
              </li>
              <li>
                <a href="#sectors" className="text-primary-foreground/80 hover:text-accent transition-colors">
                  Priority Sectors
                </a>
              </li>
              <li>
                <a href="#benefits" className="text-primary-foreground/80 hover:text-accent transition-colors">
                  Winner Benefits
                </a>
              </li>
              <li>
                <a href="#application-form" className="text-primary-foreground/80 hover:text-accent transition-colors">
                  Apply Now
                </a>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact</h4>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Phone className="w-4 h-4 text-accent" />
                <a href="tel:+923135644041" className="text-primary-foreground/80 hover:text-accent transition-colors">
                  +92 313 5644041
                </a>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="w-4 h-4 text-accent" />
                <a href="mailto:<EMAIL>" className="text-primary-foreground/80 hover:text-accent transition-colors">
                  <EMAIL>
                </a>
              </div>
            </div>

            <div className="mt-6">
              <h5 className="font-semibold mb-2">Partners</h5>
              <p className="text-sm text-primary-foreground/60">
                Pathfinder Group
              </p>
            </div>
          </div>
        </div>

        <Separator className="my-8 bg-primary-foreground/20" />

        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="text-sm text-primary-foreground/60">
            © 2025 Pathfinder CITADEL. All rights reserved.
          </div>
          <div className="flex gap-6 text-sm">
            <a href="#" className="text-primary-foreground/60 hover:text-accent transition-colors">
              Privacy Policy
            </a>
            <a href="#" className="text-primary-foreground/60 hover:text-accent transition-colors">
              Terms & Conditions
            </a>
            <a href="#" className="text-primary-foreground/60 hover:text-accent transition-colors">
              Cookie Policy
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;