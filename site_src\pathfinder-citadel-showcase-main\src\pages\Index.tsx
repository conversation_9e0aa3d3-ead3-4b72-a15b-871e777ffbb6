import Hero from "@/components/Hero";
import ValueProposition from "@/components/ValueProposition";
import WhyNow from "@/components/WhyNow";
import Frameworks from "@/components/Frameworks";
import PakistanGlance from "@/components/PakistanGlance";
import EcosystemInfrastructure from "@/components/EcosystemInfrastructure";
import SocialProof from "@/components/SocialProof";
import FeaturedLeadership from "@/components/FeaturedLeadership";
import StartupChallenge from "@/components/StartupChallenge";
import Timeline from "@/components/Timeline";
import ApplicationForm from "@/components/ApplicationForm";
import Contact from "@/components/Contact";
import Footer from "@/components/Footer";

const Index = () => {
  return (
    <div className="min-h-screen">
      {/* Skip to Content for Accessibility */}
      <a 
        href="#main-content" 
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-accent text-accent-foreground px-4 py-2 rounded-md font-medium"
      >
        Skip to main content
      </a>
      
      <main id="main-content">
        {/* Hero Section */}
        <Hero />
        
        {/* Value Proposition */}
        <ValueProposition />
        
        {/* Why Now - Crisis to Opportunity */}
        <WhyNow />
        
        {/* Frameworks That Power CITADEL */}
        <Frameworks />
        
        {/* Pakistan at a Glance */}
        <PakistanGlance />
        
        {/* Ecosystem & Infrastructure */}
        <EcosystemInfrastructure />
        
        {/* Social Proof */}
        <SocialProof />
        
        {/* Featured Leadership */}
        <FeaturedLeadership />
        
        {/* Featured Initiative: Startup Challenge */}
        <section id="startup-challenge">
          <StartupChallenge />
          <Timeline />
          <ApplicationForm />
        </section>
        
        {/* Contact */}
        <Contact />
      </main>
      
      {/* Footer */}
      <Footer />
    </div>
  );
};

export default Index;
