import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Trophy, Target, Zap, Users } from "lucide-react";

const StartupChallenge = () => {
  const features = [
    {
      icon: Target,
      title: "National Competition",
      description: "A prestigious competition to select Pakistan's top 5 startups for global representation",
    },
    {
      icon: Trophy,
      title: "Elite Selection",
      description: "Rigorous selection process ensuring only the most promising startups reach Davos",
    },
    {
      icon: Zap,
      title: "Intensive Preparation",
      description: "Comprehensive bootcamp and mentorship to prepare for the international stage",
    },
    {
      icon: Users,
      title: "Expert Guidance",
      description: "Mentorship from industry leaders, investors, and international business experts",
    },
  ];

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-6 text-primary">About the Startup Challenge</h2>
            <div className="max-w-4xl mx-auto">
              <p className="text-2xl font-semibold text-accent mb-4">
                A national competition to select and prepare Pakistan's top 5 startups to represent the country at Davos 2026.
              </p>
              <p className="text-xl text-muted-foreground">
                This is more than a competition — it's Pakistan's gateway to the global startup ecosystem, 
                providing unprecedented opportunities for visibility, growth, and international expansion.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              
              return (
                <Card key={index} className="group hover:shadow-premium transition-all duration-300 hover:-translate-y-2 border-0 shadow-card">
                  <CardHeader className="flex flex-row items-center gap-4">
                    <div className="w-12 h-12 bg-gradient-accent rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Icon className="w-6 h-6 text-accent-foreground" />
                    </div>
                    <div>
                      <CardTitle className="text-xl text-primary">{feature.title}</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-muted-foreground">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Mission Statement */}
          <div className="text-center">
            <Card className="bg-gradient-primary border-0 text-primary-foreground shadow-premium">
              <CardContent className="p-12">
                <h3 className="text-3xl font-bold mb-6">Why This Matters</h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
                  <div>
                    <div className="text-3xl font-bold text-accent mb-2">Global</div>
                    <div className="text-primary-foreground/80">Visibility before world leaders & investors</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-accent mb-2">Media</div>
                    <div className="text-primary-foreground/80">International media coverage</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-accent mb-2">Expert</div>
                    <div className="text-primary-foreground/80">Mentorship & acceleration</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-accent mb-2">Market</div>
                    <div className="text-primary-foreground/80">Gateway to international markets</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default StartupChallenge;