import { <PERSON><PERSON> } from "@/components/ui/button";
import { Building, Users, Globe, TrendingUp } from "lucide-react";

const About = () => {
  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div>
              <div className="mb-8">
                <h2 className="text-4xl font-bold mb-6 text-primary">About Pathfinder CITADEL</h2>
                <p className="text-xl text-muted-foreground mb-6 leading-relaxed">
                  Pathfinder CITADEL (Center for Innovation, Technological Advancement, Digital Entrepreneurs & Leadership) 
                  is an initiative by Pathfinder Group, dedicated to showcasing innovation and accelerating Pakistan's 
                  most promising startups onto the global stage.
                </p>
                <p className="text-lg text-muted-foreground mb-8">
                  Our mission is to identify, nurture, and elevate exceptional Pakistani startups, providing them with 
                  the platform, mentorship, and exposure needed to compete on an international level.
                </p>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-6 mb-8">
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-accent mb-1">15,000+</div>
                  <div className="text-sm text-muted-foreground">Employees</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-accent mb-1">132+</div>
                  <div className="text-sm text-muted-foreground">Cities</div>
                </div>
              </div>

              <Button variant="outline" size="lg">
                Learn More About Pathfinder Group
              </Button>
            </div>

            {/* Features Grid */}
            <div className="grid grid-cols-2 gap-6">
              <div className="text-center p-6 bg-card rounded-lg shadow-card hover:shadow-premium transition-all duration-300">
                <Building className="mx-auto mb-4 h-12 w-12 text-accent" />
                <h3 className="font-semibold text-primary mb-2">Financial Inclusion</h3>
                <p className="text-sm text-muted-foreground">Expertise in financial technology and inclusion</p>
              </div>
              
              <div className="text-center p-6 bg-card rounded-lg shadow-card hover:shadow-premium transition-all duration-300">
                <Users className="mx-auto mb-4 h-12 w-12 text-accent" />
                <h3 className="font-semibold text-primary mb-2">Global Network</h3>
                <p className="text-sm text-muted-foreground">Extensive international business network</p>
              </div>
              
              <div className="text-center p-6 bg-card rounded-lg shadow-card hover:shadow-premium transition-all duration-300">
                <Globe className="mx-auto mb-4 h-12 w-12 text-accent" />
                <h3 className="font-semibold text-primary mb-2">Market Access</h3>
                <p className="text-sm text-muted-foreground">Gateway to international markets</p>
              </div>
              
              <div className="text-center p-6 bg-card rounded-lg shadow-card hover:shadow-premium transition-all duration-300">
                <TrendingUp className="mx-auto mb-4 h-12 w-12 text-accent" />
                <h3 className="font-semibold text-primary mb-2">Growth Focus</h3>
                <p className="text-sm text-muted-foreground">Accelerating startup growth and innovation</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;