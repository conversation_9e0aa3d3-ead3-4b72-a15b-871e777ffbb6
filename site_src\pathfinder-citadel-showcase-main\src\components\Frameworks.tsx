import { Card, CardContent } from "@/components/ui/card";
import { Network, Lightbulb } from "lucide-react";

const Frameworks = () => {
  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-6 text-primary">Frameworks That Power CITADEL</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Our approach is built on proven innovation frameworks that create sustainable ecosystems for growth and collaboration.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {/* Triple Helix */}
          <Card className="shadow-card hover:shadow-premium transition-all duration-300">
            <CardContent className="p-8">
              <div className="w-16 h-16 mx-auto mb-6 bg-gradient-accent rounded-full flex items-center justify-center">
                <Network className="w-8 h-8 text-accent-foreground" />
              </div>
              <h3 className="text-2xl font-bold text-primary mb-4 text-center">Triple Helix Model</h3>
              <p className="text-muted-foreground mb-6 text-center">
                Creating synergies between Government, Academia, and Industry for sustainable innovation ecosystems.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-accent rounded-full flex-shrink-0"></div>
                  <span className="text-sm"><strong>Government:</strong> Policy framework and infrastructure support</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-accent rounded-full flex-shrink-0"></div>
                  <span className="text-sm"><strong>Academia:</strong> Research capabilities and talent pipelines</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-accent rounded-full flex-shrink-0"></div>
                  <span className="text-sm"><strong>Industry:</strong> Market demand and commercialization pathways</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* EDIE Framework */}
          <Card className="shadow-card hover:shadow-premium transition-all duration-300">
            <CardContent className="p-8">
              <div className="w-16 h-16 mx-auto mb-6 bg-gradient-accent rounded-full flex items-center justify-center">
                <Lightbulb className="w-8 h-8 text-accent-foreground" />
              </div>
              <h3 className="text-2xl font-bold text-primary mb-4 text-center">EDIE Framework</h3>
              <p className="text-muted-foreground mb-6 text-center">
                Entrepreneur-Driven Innovation Ecosystem implemented through STPs, SoEs, and accelerators.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-accent rounded-full flex-shrink-0"></div>
                  <span className="text-sm"><strong>E:</strong> Entrepreneur-centric approach</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-accent rounded-full flex-shrink-0"></div>
                  <span className="text-sm"><strong>D:</strong> Data-driven decision making</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-accent rounded-full flex-shrink-0"></div>
                  <span className="text-sm"><strong>I:</strong> Innovation ecosystem development</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-accent rounded-full flex-shrink-0"></div>
                  <span className="text-sm"><strong>E:</strong> Execution excellence</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default Frameworks;