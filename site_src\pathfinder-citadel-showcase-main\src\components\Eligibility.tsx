import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, FileText, Video, Users } from "lucide-react";

const Eligibility = () => {
  const requirements = [
    {
      icon: CheckCircle,
      title: "Pakistani Startups",
      description: "Early-stage to growth-stage Pakistani startups with working product or strong prototype",
    },
    {
      icon: FileText,
      title: "Documentation",
      description: "One-pager summary, comprehensive pitch deck (PDF), and detailed team bios",
    },
    {
      icon: Video,
      title: "Demo Video",
      description: "Optional 1–3 minute demo video showcasing your product or service (YouTube/Vimeo)",
    },
    {
      icon: Users,
      title: "Team Information",
      description: "Complete founder and team information with contact details",
    },
  ];

  const criteria = [
    "Pakistani startup with registered business or clear registration path",
    "Working product, prototype, or minimum viable product (MVP)",
    "Scalable business model with growth potential",
    "Technology-focused solution in priority sectors",
    "Strong founding team with relevant experience",
    "Clear vision for international expansion",
  ];

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4 text-primary">Eligibility & Requirements</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Who can apply and what you need to submit for the Pathfinder CITADEL DAVOS Startup Challenge
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          {/* Application Requirements */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {requirements.map((requirement, index) => {
              const Icon = requirement.icon;
              
              return (
                <Card key={index} className="text-center shadow-card hover:shadow-premium transition-all duration-300 border-0">
                  <CardHeader>
                    <div className="mx-auto mb-4 w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center">
                      <Icon className="w-8 h-8 text-accent-foreground" />
                    </div>
                    <CardTitle className="text-lg text-primary">{requirement.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-muted-foreground">
                      {requirement.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Eligibility Criteria */}
          <Card className="max-w-4xl mx-auto shadow-card border-0">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl text-primary">Eligibility Criteria</CardTitle>
              <CardDescription>
                Ensure your startup meets these requirements before applying
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {criteria.map((criterion, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <CheckCircle className="w-5 h-5 text-accent mt-0.5 flex-shrink-0" />
                    <span className="text-muted-foreground">{criterion}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default Eligibility;