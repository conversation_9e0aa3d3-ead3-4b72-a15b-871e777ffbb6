import { Brain, CreditCard, Leaf, Heart, Cog } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

const PrioritySectors = () => {
  const sectors = [
    {
      icon: Brain,
      name: "AI & ML",
      description: "Artificial Intelligence and Machine Learning solutions",
    },
    {
      icon: CreditCard,
      name: "Fintech",
      description: "Financial technology and digital payment innovations",
    },
    {
      icon: Leaf,
      name: "Climate / Greentech",
      description: "Sustainable technology and environmental solutions",
    },
    {
      icon: Heart,
      name: "HealthTech",
      description: "Healthcare technology and medical innovations",
    },
    {
      icon: Cog,
      name: "DeepTech / Industry 4.0",
      description: "Advanced manufacturing and industrial technology",
    },
  ];

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4 text-primary">Priority Sectors</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            We're seeking innovative startups across key technology sectors shaping the future
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 max-w-6xl mx-auto">
          {sectors.map((sector, index) => {
            const Icon = sector.icon;
            
            return (
              <Card 
                key={index} 
                className="group hover:shadow-premium transition-all duration-300 hover:-translate-y-2 border-0 shadow-card"
              >
                <CardContent className="p-6 text-center">
                  <div className="mx-auto mb-4 w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-8 h-8 text-accent-foreground" />
                  </div>
                  <h3 className="text-lg font-bold text-primary mb-2">{sector.name}</h3>
                  <p className="text-sm text-muted-foreground">{sector.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default PrioritySectors;