import { Calendar, Users, Trophy, Zap, Globe } from "lucide-react";

const Timeline = () => {
  const timelineItems = [
    {
      date: "Sept 13–25, 2025",
      title: "Call for Applications",
      description: "Application window opens for Pakistani startups",
      icon: Calendar,
      status: "upcoming" as const,
    },
    {
      date: "Oct 11, 2025",
      title: "First Round Pitches",
      description: "Virtual pitch sessions with expert judges",
      icon: Users,
      status: "upcoming" as const,
    },
    {
      date: "Oct 25, 2025",
      title: "National Finals",
      description: "Top startups compete in final selection round",
      icon: Trophy,
      status: "upcoming" as const,
    },
    {
      date: "Nov–Dec 2025",
      title: "Bootcamp & Mentorship",
      description: "Intensive preparation and acceleration program",
      icon: Zap,
      status: "upcoming" as const,
    },
    {
      date: "Jan 19–23, 2026",
      title: "Davos Showcase",
      description: "Present at Pakistan Pavilion, World Economic Forum",
      icon: Globe,
      status: "upcoming" as const,
    },
  ];

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4 text-primary">Timeline & Application Process</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            A clear roadmap from application to showcasing at Davos — your journey to global recognition
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-4 md:left-1/2 top-0 bottom-0 w-0.5 bg-gradient-primary transform md:-translate-x-0.5" />
            
            {timelineItems.map((item, index) => {
              const Icon = item.icon;
              const isEven = index % 2 === 0;
              
              return (
                <div key={index} className={`relative flex items-center mb-12 ${isEven ? 'md:flex-row-reverse' : ''}`}>
                  {/* Timeline Point */}
                  <div className="absolute left-0 md:left-1/2 transform md:-translate-x-1/2 z-10">
                    <div className="w-8 h-8 bg-accent rounded-full flex items-center justify-center shadow-accent">
                      <Icon className="w-4 h-4 text-accent-foreground" />
                    </div>
                  </div>
                  
                  {/* Content */}
                  <div className={`ml-12 md:ml-0 md:w-1/2 ${isEven ? 'md:pr-12' : 'md:pl-12'}`}>
                    <div className={`bg-card rounded-lg p-6 shadow-card hover:shadow-premium transition-all duration-300 ${isEven ? 'md:text-right' : ''}`}>
                      <div className="text-sm font-medium text-accent mb-2">{item.date}</div>
                      <h3 className="text-xl font-bold text-primary mb-2">{item.title}</h3>
                      <p className="text-muted-foreground">{item.description}</p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Timeline;