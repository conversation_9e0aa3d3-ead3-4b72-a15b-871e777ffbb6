@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Brand Colors - Navy Blue & Gold Theme */
    --primary: 214 73% 14%; /* #08203A - Navy Blue */
    --primary-foreground: 0 0% 98%;
    --primary-glow: 214 73% 24%;
    
    --accent: 45 73% 47%; /* #C49A2C - Gold */
    --accent-foreground: 214 73% 14%;
    --accent-light: 45 73% 57%;
    
    --secondary: 184 100% 33%; /* #0EA5A4 - Teal */
    --secondary-foreground: 0 0% 98%;
    
    /* Background & Surfaces */
    --background: 210 25% 98%; /* #F7FAFB - Very light off-white */
    --foreground: 220 14% 42%; /* #6B7280 - Muted text */
    
    --card: 0 0% 100%;
    --card-foreground: 220 14% 42%;
    
    --muted: 210 25% 96%;
    --muted-foreground: 220 14% 42%;
    
    /* Borders & Inputs */
    --border: 214 20% 88%;
    --input: 214 20% 88%;
    --ring: 45 73% 47%;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-accent: linear-gradient(135deg, hsl(var(--accent)), hsl(var(--accent-light)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-glow)) 50%, hsl(var(--accent)) 100%);
    
    /* Shadows */
    --shadow-premium: 0 20px 60px -15px hsl(var(--primary) / 0.3);
    --shadow-accent: 0 10px 30px -10px hsl(var(--accent) / 0.3);
    --shadow-card: 0 4px 20px -4px hsl(var(--primary) / 0.15);
    
    /* Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 14% 42%;
    --radius: 0.75rem;
  }

  .dark {
    /* Dark mode colors - keeping consistent with brand */
    --background: 214 73% 14%;
    --foreground: 0 0% 98%;
    --card: 214 70% 16%;
    --card-foreground: 0 0% 98%;
    --popover: 214 70% 16%;
    --popover-foreground: 0 0% 98%;
    --primary: 45 73% 47%;
    --primary-foreground: 214 73% 14%;
    --secondary: 214 50% 20%;
    --secondary-foreground: 0 0% 98%;
    --muted: 214 50% 20%;
    --muted-foreground: 220 14% 65%;
    --accent: 45 73% 47%;
    --accent-foreground: 214 73% 14%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 214 50% 25%;
    --input: 214 50% 25%;
    --ring: 45 73% 47%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .animate-fade-in {
    @apply animate-[fadeIn_0.5s_ease-out];
  }

  .animate-slide-up {
    @apply animate-[slideUp_0.6s_ease-out];
  }

  .animate-scale-in {
    @apply animate-[scaleIn_0.4s_ease-out];
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
