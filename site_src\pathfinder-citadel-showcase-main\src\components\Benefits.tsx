import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Globe, Camera, Users, TrendingUp, MapPin } from "lucide-react";

const Benefits = () => {
  const benefits = [
    {
      icon: Globe,
      title: "Global Visibility",
      description: "Present your startup before world leaders, investors, and international media at the most prestigious economic forum.",
    },
    {
      icon: Camera,
      title: "International Media Coverage",
      description: "Gain worldwide exposure through comprehensive media coverage during the World Economic Forum events.",
    },
    {
      icon: Users,
      title: "Expert Mentorship & Acceleration",
      description: "Receive intensive mentorship from industry experts and acceleration support throughout the journey.",
    },
    {
      icon: TrendingUp,
      title: "Market Access & Investment",
      description: "Connect with international investors and gain access to global markets and business opportunities.",
    },
    {
      icon: MapPin,
      title: "Pakistan Pavilion Exhibition",
      description: "Dedicated exhibition space at the Pakistan Pavilion with full travel and accommodation support.",
    },
  ];

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4 text-primary">Winner Benefits</h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Top 5 selected startups will receive comprehensive support and unprecedented opportunities
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto mb-12">
          {benefits.slice(0, 3).map((benefit, index) => {
            const Icon = benefit.icon;
            
            return (
              <Card 
                key={index} 
                className="group hover:shadow-premium transition-all duration-300 hover:-translate-y-2 border-0 shadow-card h-full"
              >
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-8 h-8 text-accent-foreground" />
                  </div>
                  <CardTitle className="text-primary">{benefit.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center text-muted-foreground">
                    {benefit.description}
                  </CardDescription>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {benefits.slice(3).map((benefit, index) => {
            const Icon = benefit.icon;
            
            return (
              <Card 
                key={index + 3} 
                className="group hover:shadow-premium transition-all duration-300 hover:-translate-y-2 border-0 shadow-card h-full"
              >
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-8 h-8 text-accent-foreground" />
                  </div>
                  <CardTitle className="text-primary">{benefit.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center text-muted-foreground">
                    {benefit.description}
                  </CardDescription>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="text-center mt-12">
          <div className="bg-accent/10 rounded-lg p-6 max-w-2xl mx-auto">
            <p className="text-lg font-semibold text-primary mb-2">
              Comprehensive Support Package
            </p>
            <p className="text-muted-foreground">
              Selected startups receive full travel, accommodation, and exhibition support for the Davos showcase, 
              plus ongoing mentorship and business development assistance.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Benefits;