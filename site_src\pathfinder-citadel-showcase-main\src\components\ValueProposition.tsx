import { <PERSON>, CardContent } from "@/components/ui/card";
import { Grad<PERSON><PERSON>ap, Rocket, Building2 } from "lucide-react";

const ValueProposition = () => {
  const pillars = [
    {
      icon: GraduationCap,
      title: "Career JumpStart Institute",
      description: "Empowering students and fresh graduates with digital skills, employability, and global readiness"
    },
    {
      icon: Rocket,
      title: "Escape Velocity Accelerator", 
      description: "Accelerating startups from seed to pre-Series A with product-market fit and investor readiness"
    },
    {
      icon: Building2,
      title: "Strategic Innovation Consulting",
      description: "Transforming enterprises and public sector with innovation strategy and digital transformation"
    }
  ];

  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-6 text-primary">
            CITADEL is Pakistan's Center for Innovation, Technological Advancement, Digital Entrepreneurs and Leadership
          </h2>
          <p className="text-xl text-muted-foreground max-w-4xl mx-auto">
            Empowering Pakistan's innovation potential to change the world through comprehensive programs that bridge talent, startups, and enterprises.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {pillars.map((pillar, index) => {
            const Icon = pillar.icon;
            return (
              <Card key={index} className="shadow-card hover:shadow-premium transition-all duration-300 group">
                <CardContent className="p-8 text-center">
                  <div className="w-20 h-20 mx-auto mb-6 bg-gradient-accent rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-10 h-10 text-accent-foreground" />
                  </div>
                  <h3 className="text-xl font-bold text-primary mb-4">{pillar.title}</h3>
                  <p className="text-muted-foreground leading-relaxed">{pillar.description}</p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default ValueProposition;